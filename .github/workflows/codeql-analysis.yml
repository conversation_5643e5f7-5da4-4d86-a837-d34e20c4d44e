name: "CodeQL"

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
  - cron: '34 5 * * *' # daily at 5:34am UTC

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-24.04

    strategy:
      fail-fast: false
      matrix:
        language:
        - 'go'

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: ${{ matrix.language }}

    - name: Autobuild
      uses: github/codeql-action/autobuild@v3

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
